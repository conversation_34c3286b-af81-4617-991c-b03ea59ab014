{"version": 3, "file": "LifecycleWatcher.js", "sourceRoot": "", "sources": ["../../../../src/cdp/LifecycleWatcher.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAIH,OAAO,EAAa,UAAU,EAAC,MAAM,iBAAiB,CAAC;AAIvD,OAAO,EAAC,YAAY,EAAC,MAAM,2BAA2B,CAAC;AACvD,OAAO,EAAC,mBAAmB,EAAC,MAAM,mCAAmC,CAAC;AACtE,OAAO,EAAC,MAAM,EAAC,MAAM,mBAAmB,CAAC;AACzC,OAAO,EAAC,QAAQ,EAAC,MAAM,qBAAqB,CAAC;AAC7C,OAAO,EAAC,eAAe,EAAC,MAAM,uBAAuB,CAAC;AAGtD,OAAO,EAAC,iBAAiB,EAAC,MAAM,yBAAyB,CAAC;AAmC1D,MAAM,4BAA4B,GAAG,IAAI,GAAG,CAG1C;IACA,CAAC,MAAM,EAAE,MAAM,CAAC;IAChB,CAAC,kBAAkB,EAAE,kBAAkB,CAAC;IACxC,CAAC,cAAc,EAAE,aAAa,CAAC;IAC/B,CAAC,cAAc,EAAE,mBAAmB,CAAC;CACtC,CAAC,CAAC;AAEH;;GAEG;AACH,MAAM,OAAO,gBAAgB;IAC3B,kBAAkB,CAA2B;IAC7C,MAAM,CAAW;IACjB,QAAQ,CAAS;IACjB,kBAAkB,GAAuB,IAAI,CAAC;IAC9C,cAAc,GAAG,IAAI,eAAe,EAAE,CAAC;IACvC,gBAAgB,CAAS;IAEzB,oBAAoB,CAAkB;IACtC,+BAA+B,GAAG,QAAQ,CAAC,MAAM,EAAa,CAAC;IAC/D,kBAAkB,GAAG,QAAQ,CAAC,MAAM,EAAQ,CAAC;IAC7C,8BAA8B,GAAG,QAAQ,CAAC,MAAM,EAAa,CAAC;IAC9D,MAAM,GAAG,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;IAElD,0BAA0B,CAAW;IACrC,QAAQ,CAAW;IAEnB,2BAA2B,CAAkB;IAE7C,YACE,cAA8B,EAC9B,KAAe,EACf,SAA8D,EAC9D,OAAe,EACf,MAAoB;QAEpB,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;YAC7B,SAAS,GAAG,SAAS,CAAC,KAAK,EAAE,CAAC;QAChC,CAAC;aAAM,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE,CAAC;YACzC,SAAS,GAAG,CAAC,SAAS,CAAC,CAAC;QAC1B,CAAC;QACD,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,SAAS,CAAC;QACxC,IAAI,CAAC,kBAAkB,GAAG,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;YAC9C,MAAM,aAAa,GAAG,4BAA4B,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAC9D,MAAM,CAAC,aAAa,EAAE,uCAAuC,GAAG,KAAK,CAAC,CAAC;YACvE,OAAO,aAAa,CAAC;QACvB,CAAC,CAAC,CAAC;QAEH,MAAM,EAAE,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;YACrC,IAAI,MAAM,CAAC,MAAM,YAAY,KAAK,EAAE,CAAC;gBACnC,MAAM,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;YACpC,CAAC;YACD,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,MAAM,mBAAmB,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CACjD,IAAI,YAAY,CAAC,KAAK,CAAC,aAAa,CAAC,CACtC,CAAC;QACF,mBAAmB,CAAC,EAAE,CACpB,iBAAiB,CAAC,cAAc,EAChC,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC,CACxC,CAAC;QAEF,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;QACtE,YAAY,CAAC,EAAE,CACb,UAAU,CAAC,4BAA4B,EACvC,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,CAAC,CACzC,CAAC;QACF,YAAY,CAAC,EAAE,CAAC,UAAU,CAAC,cAAc,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACvE,YAAY,CAAC,EAAE,CAAC,UAAU,CAAC,YAAY,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACxE,YAAY,CAAC,EAAE,CACb,UAAU,CAAC,wBAAwB,EACnC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAC9B,CAAC;QACF,YAAY,CAAC,EAAE,CAAC,UAAU,CAAC,aAAa,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAE5E,MAAM,qBAAqB,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CACnD,IAAI,YAAY,CAAC,cAAc,CAAC,CACjC,CAAC;QACF,qBAAqB,CAAC,EAAE,CACtB,mBAAmB,CAAC,OAAO,EAC3B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAC3B,CAAC;QACF,qBAAqB,CAAC,EAAE,CACtB,mBAAmB,CAAC,QAAQ,EAC5B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAC5B,CAAC;QACF,qBAAqB,CAAC,EAAE,CACtB,mBAAmB,CAAC,aAAa,EACjC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CACjC,CAAC;QAEF,IAAI,CAAC,oBAAoB,GAAG,QAAQ,CAAC,MAAM,CAAQ;YACjD,OAAO,EAAE,IAAI,CAAC,QAAQ;YACtB,OAAO,EAAE,yBAAyB,IAAI,CAAC,QAAQ,cAAc;SAC9D,CAAC,CAAC;QAEH,IAAI,CAAC,uBAAuB,EAAE,CAAC;IACjC,CAAC;IAED,UAAU,CAAC,OAAoB;QAC7B,IAAI,OAAO,CAAC,KAAK,EAAE,KAAK,IAAI,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE,EAAE,CAAC;YACtE,OAAO;QACT,CAAC;QACD,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC;QAClC,kEAAkE;QAClE,yEAAyE;QACzE,yCAAyC;QACzC,IAAI,CAAC,2BAA2B,EAAE,OAAO,EAAE,CAAC;QAC5C,IAAI,CAAC,2BAA2B,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC;QACrD,IAAI,OAAO,CAAC,QAAQ,EAAE,KAAK,IAAI,EAAE,CAAC;YAChC,IAAI,CAAC,2BAA2B,EAAE,OAAO,EAAE,CAAC;QAC9C,CAAC;IACH,CAAC;IAED,gBAAgB,CAAC,OAAoB;QACnC,IAAI,IAAI,CAAC,kBAAkB,EAAE,EAAE,KAAK,OAAO,CAAC,EAAE,EAAE,CAAC;YAC/C,OAAO;QACT,CAAC;QACD,IAAI,CAAC,2BAA2B,EAAE,OAAO,EAAE,CAAC;IAC9C,CAAC;IAED,WAAW,CAAC,QAAsB;QAChC,IAAI,IAAI,CAAC,kBAAkB,EAAE,EAAE,KAAK,QAAQ,CAAC,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC;YAC1D,OAAO;QACT,CAAC;QACD,IAAI,CAAC,2BAA2B,EAAE,OAAO,EAAE,CAAC;IAC9C,CAAC;IAED,gBAAgB,CAAC,KAAY;QAC3B,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK,EAAE,CAAC;YAC1B,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,+BAA+B,CAAC;YACtD,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC/C,OAAO;QACT,CAAC;QACD,IAAI,CAAC,uBAAuB,EAAE,CAAC;IACjC,CAAC;IAED,KAAK,CAAC,kBAAkB;QACtB,0CAA0C;QAC1C,MAAM,IAAI,CAAC,2BAA2B,EAAE,YAAY,EAAE,CAAC;QACvD,OAAO,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;IAC7E,CAAC;IAED,6BAA6B;QAC3B,OAAO,IAAI,CAAC,+BAA+B,CAAC,YAAY,EAAE,CAAC;IAC7D,CAAC;IAED,4BAA4B;QAC1B,OAAO,IAAI,CAAC,8BAA8B,CAAC,YAAY,EAAE,CAAC;IAC5D,CAAC;IAED,gBAAgB;QACd,OAAO,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;IAChD,CAAC;IAED,kBAAkB;QAChB,OAAO,IAAI,CAAC,oBAAoB,CAAC,YAAY,EAAE,CAAC;IAClD,CAAC;IAED,wBAAwB;QACtB,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC;QACvC,IAAI,CAAC,uBAAuB,EAAE,CAAC;IACjC,CAAC;IAED,UAAU,CAAC,cAA4C;QACrD,IAAI,cAAc,KAAK,yBAAyB,EAAE,CAAC;YACjD,OAAO,IAAI,CAAC,aAAa,EAAE,CAAC;QAC9B,CAAC;QACD,IAAI,CAAC,uBAAuB,EAAE,CAAC;IACjC,CAAC;IAED,aAAa;QACX,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,uBAAuB,EAAE,CAAC;IACjC,CAAC;IAED,uBAAuB;QACrB,kCAAkC;QAClC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC;YAC1D,OAAO;QACT,CAAC;QACD,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;QAClC,IAAI,IAAI,CAAC,0BAA0B,EAAE,CAAC;YACpC,IAAI,CAAC,+BAA+B,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAC1D,CAAC;QACD,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,KAAK,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACrE,IAAI,CAAC,8BAA8B,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QACzD,CAAC;QAED,SAAS,cAAc,CACrB,KAAe,EACf,iBAA2C;YAE3C,KAAK,MAAM,KAAK,IAAI,iBAAiB,EAAE,CAAC;gBACtC,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;oBACvC,OAAO,KAAK,CAAC;gBACf,CAAC;YACH,CAAC;YACD,KAAK,MAAM,KAAK,IAAI,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC;gBACxC,IACE,KAAK,CAAC,kBAAkB;oBACxB,CAAC,cAAc,CAAC,KAAK,EAAE,iBAAiB,CAAC,EACzC,CAAC;oBACD,OAAO,KAAK,CAAC;gBACf,CAAC;YACH,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED,OAAO;QACL,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;QAC9B,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC3D,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACjD,CAAC;CACF"}